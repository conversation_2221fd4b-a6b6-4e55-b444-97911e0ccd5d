import { Card, CardContent } from "../ui/card"
import { OceanScores } from "../../types/assessment-results"

interface WorldMapCardProps {
  title: string
  description: string
  oceanScores?: OceanScores
}

export function WorldMapCard({ title, description, oceanScores }: WorldMapCardProps) {
  // Default Ocean scores from latest VIAIS assessment
  const defaultScores: OceanScores = {
    openness: 50,
    conscientiousness: 48,
    extraversion: 38,
    agreeableness: 65,
    neuroticism: 52
  }

  // Use provided scores or fallback to defaults
  const scores = oceanScores || defaultScores

  // Ocean data for bar chart with colors matching the current design
  const oceanData = [
    {
      trait: 'OPNS',
      score: scores.openness,
      color: '#6475e9', // Blue
    },
    {
      trait: 'CONS',
      score: scores.conscientiousness,
      color: '#6475e9', // Blue
    },
    {
      trait: 'EXTN',
      score: scores.extraversion,
      color: '#6475e9', // Blue
    },
    {
      trait: 'AGRS',
      score: scores.agreeableness,
      color: '#6475e9', // Blue
    },
    {
      trait: 'NESM',
      score: scores.neuroticism,
      color: '#a2acf2', // Light blue for neuroticism (as in original)
    },
  ]
  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardContent className="flex flex-col space-y-1.5 p-6">
        <div className="text-center mb-6">
          <h3 className="text-lg font-semibold text-[#1e1e1e] mb-2">{title}</h3>
          <p className="text-sm text-[#64707d]">{description}</p>
        </div>

        <div className="flex items-center justify-center gap-2">
          {oceanData.map((item, index) => {
            const heightPercentage = Math.max((item.score / 100) * 100, 15) // Minimum 15% height for visibility
            return (
              <div key={item.trait} className="flex flex-col items-center gap-1 flex-1">
                {/* Percentage label positioned above the bar chart area */}
                <div className="h-6 flex items-center justify-center">
                  <span className="text-xs font-semibold text-[#1e1e1e]">
                    {item.score}%
                  </span>
                </div>
                <div
                  className="relative w-full rounded-lg overflow-hidden"
                  style={{ height: '128px', backgroundColor: '#F3F3F3' }}
                >
                  <div
                    className="absolute bottom-0 w-full transition-all duration-300"
                    style={{
                      height: `${heightPercentage}%`,
                      backgroundColor: item.color,
                      minHeight: '20px'
                    }}
                  />
                </div>
                <span className="text-xs font-medium text-[#1e1e1e]">{item.trait}</span>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
